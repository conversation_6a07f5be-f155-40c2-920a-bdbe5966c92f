<template>
    <div class="deliver-container">
        <!-- 顶部操作栏 -->
        <div class="top-actions">
            <div class="action-buttons">
                <el-button
                    type="success"
                    size="small"
                    icon="el-icon-position"
                    @click="openPromoteDialog"
                    >投放</el-button
                >
                <el-button
                    type="info"
                    size="small"
                    icon="el-icon-refresh"
                    @click="refreshData"
                    :loading="refreshLoading"
                    >刷新</el-button
                >
            </div>
            <div class="coin-info" v-if="promotionCoins > 0">
                <span class="coin-label">投放币:</span>
                <span class="coin-value">{{ promotionCoins }}</span>
            </div>
        </div>

        <!-- 已投放商品 - 表格形式 -->
        <div class="promoted-goods-container">
            <div class="promoted-header">
                <h3>已投放商品</h3>
                <!-- 添加Tab切换 -->
                <el-tabs
                    v-model="activeTab"
                    @tab-click="handleTabClick"
                    class="promoted-tabs"
                >
                    <el-tab-pane label="投放中" name="1"></el-tab-pane>
                    <el-tab-pane label="投放结束" name="2"></el-tab-pane>
                </el-tabs>
            </div>
            <el-table
                v-loading="promotedLoading"
                :data="promotedGoods"
                style="width: 100%"
                size="small"
                border
                stripe
                class="promoted-table"
            >
                <el-table-column label="商品" min-width="180">
                    <template slot-scope="scope">
                        <div class="promoted-product-cell">
                            <div class="promoted-image">
                                <img
                                    :src="scope.row.banner_img"
                                    alt="商品图片"
                                />
                            </div>
                            <div class="promoted-title-container">
                                <div class="promoted-title">
                                    {{ scope.row.title }}
                                </div>
                                <div class="promoted-brief">
                                    {{ scope.row.brief }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="period"
                    label="期数"
                    width="70"
                    align="center"
                ></el-table-column>
                <el-table-column label="类型" width="90" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.type == 1 ? "固定位置" : "滑动位置" }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="sort"
                    label="投放排名"
                    width="90"
                    align="center"
                ></el-table-column>
                <el-table-column label="当前排名" width="90" align="center">
                    <template slot-scope="scope">
                        <span
                            :class="{
                                'rank-worse':
                                    scope.row.rank > scope.row.before_rank,
                            }"
                        >
                            {{ scope.row.rank }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="before_rank"
                    label="原始排名"
                    width="90"
                    align="center"
                ></el-table-column>

                <el-table-column label="价格" width="100" align="center">
                    <template slot-scope="scope">
                        <span class="promoted-price"
                            >¥{{ scope.row.price }}</span
                        >
                    </template>
                </el-table-column>

                <el-table-column label="当前分值" width="80" align="center">
                    <template slot-scope="scope">
                        <span
                            class="promoted-score"
                            @click="showScoreDetails(scope.row)"
                            >{{ scope.row.score.toFixed(2) }}</span
                        >
                    </template>
                </el-table-column>

                <el-table-column
                    prop="purchased"
                    label="已售"
                    width="70"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="uname"
                    label="投放人"
                    width="80"
                    align="center"
                ></el-table-column>

                <el-table-column
                    prop="start_time"
                    label="开始时间"
                    width="150"
                    align="center"
                ></el-table-column>
                <el-table-column
                    prop="end_time"
                    label="结束时间"
                    width="150"
                    align="center"
                ></el-table-column>
                <!-- 取消投放的按钮 - 只在投放中tab显示 -->
                <el-table-column
                    v-if="activeTab === '1'"
                    label="操作"
                    width="110"
                    align="center"
                >
                    <template slot-scope="scope">
                        <el-button
                            type="danger"
                            size="mini"
                            @click="cancelPromotion(scope.row)"
                            :loading="scope.row.cancelLoading"
                        >
                            取消投放
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" v-if="promotedTotal > 0">
                <el-pagination
                    @size-change="handlePromotedSizeChange"
                    @current-change="handlePromotedCurrentChange"
                    :current-page="promotedQuery.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="promotedQuery.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="promotedTotal"
                >
                </el-pagination>
            </div>
        </div>

        <!-- 投放弹窗 -->
        <el-dialog
            title="商品投放"
            :visible.sync="promoteDialogVisible"
            width="600px"
            :close-on-click-modal="false"
        >
            <div class="promote-form">
                <el-form label-position="top" :model="promoteForm" size="small">
                    <!-- 商品搜索/选择 -->
                    <el-form-item label="选择商品">
                        <el-input
                            v-model="searchKeyword"
                            placeholder="输入商品期数"
                            prefix-icon="el-icon-search"
                            clearable
                            @blur="searchGoods"
                            @keyup.enter.native="searchGoods"
                        ></el-input>

                        <div
                            class="search-results"
                            v-if="searchResults.length > 0"
                        >
                            <el-radio-group
                                v-model="promoteForm.selectedGoodId"
                            >
                                <div
                                    v-for="item in searchResults"
                                    :key="item.id"
                                    class="search-item"
                                >
                                    <el-radio :label="item.id">
                                        <div class="search-item-content">
                                            <div class="search-item-image">
                                                <img
                                                    :src="item.imageUrl"
                                                    alt="商品图片"
                                                />
                                            </div>
                                            <div class="search-item-info">
                                                <div class="search-item-title">
                                                    {{ item.title }}
                                                </div>
                                                <div class="search-item-price">
                                                    ¥{{ item.price }}
                                                </div>
                                            </div>
                                        </div>
                                    </el-radio>
                                </div>
                            </el-radio-group>
                        </div>
                        <div
                            class="no-results"
                            v-else-if="searchKeyword && !loading"
                        >
                            未找到相关商品
                        </div>
                    </el-form-item>

                    <!-- 投放时间 - 分开的开始时间和结束时间 -->
                    <el-form-item label="开始时间">
                        <el-date-picker
                            v-model="promoteForm.startTime"
                            type="datetime"
                            placeholder="选择开始时间"
                            value-format="yyyy-MM-dd HH:mm"
                            format="yyyy-MM-dd HH:mm"
                            :disabled="!promoteForm.selectedGoodId"
                            style="width: 100%"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间">
                        <el-date-picker
                            v-model="promoteForm.endTime"
                            type="datetime"
                            placeholder="选择结束时间"
                            value-format="yyyy-MM-dd HH:mm"
                            format="yyyy-MM-dd HH:mm"
                            :disabled="
                                !promoteForm.selectedGoodId ||
                                !promoteForm.startTime
                            "
                            :picker-options="{
                                disabledDate(time) {
                                    return (
                                        promoteForm.startTime &&
                                        time.getTime() <
                                            new Date(
                                                promoteForm.startTime
                                            ).getTime()
                                    );
                                },
                            }"
                            style="width: 100%"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="目标投放位置">
                        <el-input-number
                            v-model="promoteForm.sort"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="类型">
                        <el-radio-group v-model="promoteForm.type">
                            <el-radio :label="1">固定位置</el-radio>
                            <el-radio :label="2">滑动位置</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="promoteDialogVisible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="submitPromotion"
                    :disabled="!canPromote"
                >
                    确认投放
                </el-button>
            </span>
        </el-dialog>

        <!-- 评分详情弹窗 -->
        <el-dialog
            title="评分详情"
            :visible.sync="scoreDetailsDialogVisible"
            width="800px"
            height="1000px"
            :close-on-click-modal="true"
        >
            <div class="score-details-container">
                <div class="score-details-header">
                    <div class="total-score">
                        <div class="score-value">
                            {{ currentItemScore.toFixed(2) }}
                        </div>
                        <div class="score-label">总分</div>
                    </div>
                </div>
                <div class="score-details-list two-columns">
                    <div
                        v-for="(item, index) in scoreDetails"
                        :key="index"
                        class="score-detail-item"
                    >
                        <div class="score-detail-label">{{ item.label }}</div>
                        <div class="score-detail-value">{{ item.value }}</div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "DeliverPage",
    data() {
        return {
            promotionCoins: 0, // 投放币余额
            searchKeyword: "",
            searchResults: [],
            promoteForm: {
                selectedGoodId: null,
                startTime: null, // 开始时间
                endTime: null, // 结束时间
                sort: 0,
                type: 1, // 类型：1固定位置，2滑动位置
            },
            promoteDialogVisible: false,
            loading: false,

            // 已投放商品列表
            promotedGoods: [],
            promotedTotal: 0,
            promotedLoading: false,
            promotedQuery: {
                status: 1, // 1:在投商品 2:投放结束
                page: 1,
                limit: 10,
            },
            activeTab: "1", // 当前激活的tab，'1'表示投放中，'2'表示投放结束
            refreshLoading: false, // 刷新按钮加载状态

            scoreDetailsDialogVisible: false, // 评分详情弹窗是否显示
            currentItemScore: 0, // 当前选中商品的评分
            scoreDetails: [], // 评分详情
        };
    },
    mounted() {
        // 获取已投放商品列表
        this.getPromotedGoods();

        // 获取投放币余额
        this.getUserLimit();
    },
    computed: {
        canPromote() {
            // 检查是否选择了商品、开始时间和结束时间
            return (
                this.promoteForm.selectedGoodId &&
                this.promoteForm.startTime &&
                this.promoteForm.endTime
            );
        },
    },
    methods: {
        openPromoteDialog() {
            this.promoteDialogVisible = true;
            this.searchKeyword = "";
            this.searchResults = [];
            this.promoteForm = {
                selectedGoodId: null,
                startTime: null,
                endTime: null,
                sort: 0,
                type: 1, // 默认选择固定位置
            };
        },

        async searchGoods() {
            if (!this.searchKeyword.trim()) {
                this.searchResults = [];
                return;
            }

            this.loading = true;
            try {
                const params = {
                    periods_type: 0, // 期数频道
                    is_leftover: 0, // 不显示尾货
                    periods: this.searchKeyword.trim(), // 搜索期数
                    page: 1,
                    limit: 10,
                };

                const res = await this.$request.coupons.getPeriodsList(params);
                if (res.data.error_code === 0) {
                    // 将API返回的数据转换为我们需要的格式
                    this.searchResults = (res.data.data.list || []).map(
                        (item) => ({
                            id: item.id,
                            title: item.title,
                            description: item.brief,
                            imageUrl: item.banner_img,
                            price: item.price,
                            soldCount: item.purchased || 0,
                            totalCount: item.inventory || 0,
                        })
                    );
                }
            } catch (error) {
                console.error("搜索期数失败:", error);
                this.$message.error("搜索期数失败");
                this.searchResults = [];
            } finally {
                this.loading = false;
            }
        },

        async submitPromotion() {
            if (!this.canPromote) return;

            // 显示确认对话框
            try {
                // 获取选中商品的信息
                const selectedGood = this.searchResults.find(
                    (item) => item.id === this.promoteForm.selectedGoodId
                );
                const goodTitle = selectedGood ? selectedGood.title : "商品";
                const startTime = this.promoteForm.startTime;
                const endTime = this.promoteForm.endTime;

                await this.$confirm(
                    `您将投放商品"${goodTitle}"，投放时间从 ${startTime} 到 ${endTime}。确定要投放吗？`,
                    "投放确认",
                    {
                        confirmButtonText: "确定投放",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                );
            } catch (error) {
                // 用户取消操作
                return;
            }

            this.$set(this, "loading", true);

            try {
                // 将时间转换为时间戳
                const startTimestamp = Math.floor(
                    new Date(this.promoteForm.startTime).getTime() / 1000
                );
                const endTimestamp = Math.floor(
                    new Date(this.promoteForm.endTime).getTime() / 1000
                );

                // 调用投放接口
                const params = {
                    period: this.promoteForm.selectedGoodId, // 商品期数
                    start_time: startTimestamp, // 开始时间（时间戳）
                    end_time: endTimestamp, // 结束时间（时间戳）
                    sort: this.promoteForm.sort,
                    type: this.promoteForm.type,
                };

                const res = await this.$request.coupons.addSort(params);

                if (res.data.error_code === 0) {
                    // 投放成功，关闭弹窗
                    this.promoteDialogVisible = false;

                    // 刷新已投放商品列表
                    await this.getPromotedGoods();

                    // 更新投放币余额
                    await this.getUserLimit();

                    // 成功提示
                    this.$message.success(
                        `商品投放成功，投放时间：${this.promoteForm.startTime} 至 ${this.promoteForm.endTime}`
                    );
                }
            } catch (error) {
                console.error("投放失败:", error);
                this.$message.error("投放失败，请重试");
            } finally {
                this.$set(this, "loading", false);
            }
        },

        // 获取已投放商品列表
        async getPromotedGoods() {
            this.promotedLoading = true;
            try {
                const res = await this.$request.coupons.getPeriodsSortList(
                    this.promotedQuery
                );
                if (res.data.error_code === 0) {
                    this.promotedGoods = res.data.data.list || [];
                    this.promotedTotal = res.data.data.total || 0;
                }
            } catch (error) {
                console.error("获取已投放商品列表失败:", error);
                this.$message.error("获取已投放商品列表失败");
            } finally {
                this.promotedLoading = false;
            }
        },

        // 处理已投放商品分页大小变化
        handlePromotedSizeChange(val) {
            this.promotedQuery.limit = val;
            this.promotedQuery.page = 1;
            this.getPromotedGoods();
        },

        // 处理已投放商品分页页码变化
        handlePromotedCurrentChange(val) {
            this.promotedQuery.page = val;
            this.getPromotedGoods();
        },

        // 处理Tab切换
        handleTabClick(tab) {
            this.activeTab = tab.name;
            this.promotedQuery.status = parseInt(tab.name); // 1:投放中 2:投放结束
            this.promotedQuery.page = 1; // 重置到第一页
            this.getPromotedGoods();
        },

        // 获取投放币余额
        async getUserLimit() {
            try {
                const res = await this.$request.coupons.getUserLimit();
                if (res.data.error_code === 0) {
                    this.promotionCoins = res.data.data.limit || 0;
                }
            } catch (error) {
                console.error("获取投放币余额失败:", error);
                this.$message.error("获取投放币余额失败");
            }
        },

        // 刷新数据
        async refreshData(showMessage = true) {
            if (this.refreshLoading) return;

            this.refreshLoading = true;
            try {
                // 重置分页到第一页
                this.promotedQuery.page = 1;

                // 刷新已投放商品列表
                await this.getPromotedGoods();

                // 更新投放币余额
                await this.getUserLimit();

                if (showMessage) {
                    this.$message.success("刷新成功");
                }
            } catch (error) {
                console.error("刷新数据失败:", error);
                if (showMessage) {
                    this.$message.error("刷新数据失败");
                }
            } finally {
                this.refreshLoading = false;
            }
        },

        // 取消投放
        async cancelPromotion(item) {
            if (!item || !item.id) {
                this.$message.error("无效的投放记录");
                return;
            }

            // 显示确认对话框
            try {
                await this.$confirm("确定要取消该商品的投放吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                });
            } catch (error) {
                // 用户取消操作
                return;
            }

            // 设置加载状态
            this.$set(item, "cancelLoading", true);

            try {
                // 调用取消投放接口
                const params = {
                    id: item.id, // 去除可能的前缀
                };

                const res = await this.$request.coupons.cancelSort(params);

                if (res.data.error_code === 0) {
                    // 取消投放成功，更新投放币余额
                    await this.getUserLimit();

                    // 刷新已投放商品列表
                    this.getPromotedGoods();

                    this.$message.success("已成功取消投放");
                }
            } catch (error) {
                console.error("取消投放失败:", error);
                this.$message.error("取消投放失败，请重试");
            } finally {
                this.$set(item, "cancelLoading", false);
            }
        },

        // 显示评分详情弹窗
        showScoreDetails(item) {
            if (!item || typeof item.score === "undefined") {
                this.$message.error("无法获取评分详情");
                return;
            }

            // 设置当前商品评分
            this.currentItemScore = item.score;

            // 显示弹窗
            this.scoreDetails = item.scoreDetails;
            this.scoreDetailsDialogVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.deliver-container {
    padding: 20px;

    .top-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .action-buttons {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .coin-info {
            display: flex;
            align-items: center;
            background-color: #f8f8f8;
            padding: 5px 12px;
            border-radius: 20px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            .coin-label {
                font-size: 14px;
                color: #666;
                margin-right: 5px;
            }

            .coin-value {
                font-size: 16px;
                font-weight: bold;
                color: #ff9900;
            }
        }
    }

    /* 已投放商品区域样式 - 表格形式 */
    .promoted-goods-container {
        width: 100%;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        padding: 15px;

        .promoted-header {
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;

            h3 {
                font-size: 16px;
                color: #333;
                margin: 0 0 15px 0;
                font-weight: 600;
            }

            .promoted-tabs {
                margin-bottom: 0;

                ::v-deep .el-tabs__header {
                    margin: 0;
                }

                ::v-deep .el-tabs__nav-wrap::after {
                    display: none;
                }

                ::v-deep .el-tabs__item {
                    padding: 0 20px;
                    height: 36px;
                    line-height: 36px;
                    font-size: 14px;

                    &.is-active {
                        color: #409eff;
                        font-weight: 600;
                    }
                }

                ::v-deep .el-tabs__active-bar {
                    background-color: #409eff;
                }
            }
        }

        .pagination-container {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
        }

        .promoted-table {
            .el-table__header th {
                background-color: #f5f7fa;
                color: #606266;
                font-weight: 600;
                padding: 8px 0;
            }

            .el-table__row {
                transition: background-color 0.2s;

                &:hover {
                    background-color: #f0f9ff !important;
                }
            }

            .rank-worse {
                background-color: #fef0f0;
                color: #f56c6c;
                padding: 4px 8px;
                border-radius: 4px;
                font-weight: 500;
            }
        }

        .promoted-product-cell {
            display: flex;
            align-items: center;

            .promoted-image {
                width: 50px;
                height: 50px;
                flex-shrink: 0;
                margin-right: 10px;
                border-radius: 4px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .promoted-title-container {
                display: flex;
                flex-direction: column;

                .promoted-title {
                    font-size: 13px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .promoted-brief {
                    font-size: 12px;
                    color: #666;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        .promoted-price {
            font-size: 14px;
            font-weight: 600;
            color: #ff6600;
        }

        .promoted-score {
            font-size: 14px;
            font-weight: 600;
            color: #409eff;
            cursor: pointer;

            &:hover {
                opacity: 0.8;
            }
        }
    }

    /* 投放弹窗样式 */
    .promote-form {
        .search-results {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
            border: 1px solid #ebeef5;
            border-radius: 4px;

            .search-item {
                padding: 10px;
                border-bottom: 1px solid #ebeef5;

                &:last-child {
                    border-bottom: none;
                }

                .search-item-content {
                    display: flex;
                    align-items: center;

                    .search-item-image {
                        width: 60px;
                        height: 60px;
                        margin-right: 10px;
                        overflow: hidden;
                        border-radius: 4px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .search-item-info {
                        flex: 1;

                        .search-item-title {
                            font-size: 14px;
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 5px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .search-item-price {
                            font-size: 14px;
                            color: #ff6600;
                            font-weight: bold;
                            margin-bottom: 5px;
                        }
                    }
                }
            }
        }

        .no-results {
            text-align: center;
            padding: 20px;
            color: #909399;
            font-size: 14px;
        }
    }

    /* 评分详情弹窗样式 */
    .score-details-container {
        padding: 10px;

        .score-details-header {
            text-align: center;
            margin-bottom: 20px;

            .total-score {
                display: inline-block;

                .score-value {
                    font-size: 36px;
                    font-weight: bold;
                    color: #409eff;
                }

                .score-label {
                    font-size: 14px;
                    color: #606266;
                    margin-top: 5px;
                }
            }
        }

        .score-details-list {
            max-height: 400px;
            overflow-y: auto;

            &.two-columns {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px 20px;
            }

            .score-detail-item {
                display: flex;
                justify-content: space-between;
                padding: 6px 15px;
                border-bottom: 1px solid #ebeef5;

                &:last-child {
                    border-bottom: none;
                }

                .score-detail-label {
                    flex: 3;
                    font-size: 14px;
                    color: #606266;
                    margin-right: 10px;
                }

                .score-detail-value {
                    flex: 1;
                    text-align: right;
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                }
            }
        }
    }
}
</style>
