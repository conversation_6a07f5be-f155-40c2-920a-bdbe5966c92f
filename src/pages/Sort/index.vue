<template>
    <div class="goods-sort-container">
        <!-- 顶部操作栏 -->
        <div class="top-actions">
            <div class="action-buttons">
                <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-setting"
                    @click="openConfigDialog"
                    >配置权重</el-button
                >
                <el-button
                    type="info"
                    size="small"
                    icon="el-icon-refresh"
                    @click="refreshData"
                    :loading="refreshLoading"
                    >刷新</el-button
                >
                <div class="auto-refresh-container">
                    <el-checkbox
                        v-model="autoRefresh"
                        @change="handleAutoRefreshChange"
                        >自动刷新</el-checkbox
                    >
                    <el-select
                        v-model="refreshInterval"
                        size="small"
                        placeholder="选择频率"
                        :disabled="!autoRefresh"
                        @change="handleIntervalChange"
                    >
                        <el-option
                            v-for="item in refreshIntervalOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </div>
        </div>
        <div class="content-wrapper">
            <!-- 商品列表 - 左侧 -->
            <div class="goods-list-container">
                <div class="goods-list" ref="goodsList">
                    <div
                        v-for="(item, index) in displayedGoods"
                        :key="index"
                        class="goods-item"
                        :data-item-id="item.id"
                    >
                        <!-- 商品图片 -->
                        <div class="goods-image">
                            <img :src="item.banner_img" alt="商品图片" />
                            <!-- 商品曝光值指示器 - 左上角 -->

                            <div
                                class="item-exposure-badge"
                                @click="showScoreDetails(item)"
                            >
                                <span style="font-size: 15px"
                                    >序号：{{ index + 1 }}
                                </span>
                                分数：{{ item.score.toFixed(2) }}
                            </div>
                            <!-- 商品期数指示器 - 右上角 -->
                            <div
                                class="item-period-badge"
                                @click="copyToClipboard(item.id)"
                            >
                                期数：{{ item.id }}
                            </div>
                        </div>

                        <!-- 商品信息 -->
                        <div class="goods-info">
                            <!-- 商品标题和标签 -->
                            <div class="goods-header">
                                <h3 class="goods-title">{{ item.title }}</h3>
                            </div>

                            <!-- 商品描述 -->
                            <div class="goods-desc">
                                <p>{{ item.brief }}</p>
                            </div>
                            <!-- 商品标签 -->
                            <div
                                class="goods-labels"
                                v-if="item.label && item.label.length > 0"
                            >
                                <el-tag
                                    v-for="(label, labelIndex) in item.label"
                                    :key="labelIndex"
                                    size="mini"
                                    type="info"
                                    class="goods-label-tag"
                                >
                                    {{ label }}
                                </el-tag>
                            </div>

                            <!-- 频道和投放类型信息 -->
                            <div class="goods-channel-info">
                                <!-- 频道类型 -->
                                <el-tag
                                    :type="getChannelTagType(item.periods_type)"
                                    size="mini"
                                    class="channel-tag"
                                >
                                    {{ getChannelName(item.periods_type) }}
                                </el-tag>
                                <!-- 投放排序类型 -->
                                <el-tag
                                    v-if="item.sortType"
                                    type="warning"
                                    size="mini"
                                    class="sort-type-tag"
                                >
                                    {{ item.sortType }}
                                </el-tag>
                                <el-tag
                                    v-else
                                    type="info"
                                    size="mini"
                                    class="sort-type-tag no-sort"
                                >
                                    未投放
                                </el-tag>
                            </div>

                            <!-- 商品底部价格信息 -->
                            <div class="goods-footer">
                                <div class="goods-price">
                                    <span class="price-symbol">¥</span>
                                    <span class="price-value">{{
                                        item.price
                                    }}</span>
                                </div>
                                <div class="goods-sales">
                                    已售{{ item.purchased
                                    }}<span v-if="item.limit_number">
                                        /限量{{ item.limit_number }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载更多指示器 -->
                    <div v-if="loading" class="loading-indicator">
                        <i class="el-icon-loading"></i> 加载中...
                    </div>

                    <!-- 无更多数据提示 -->
                    <div v-if="!hasMoreData && !loading" class="no-more-data">
                        没有更多商品了
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置权重弹窗 -->
        <el-dialog
            title="配置权重"
            :visible.sync="configDialogVisible"
            width="700px"
            :close-on-click-modal="false"
        >
            <div class="config-form">
                <el-form label-position="top" size="small">
                    <!-- 权重配置 -->
                    <div class="weight-section">
                        <h3 class="section-title">权重配置</h3>
                        <div class="weight-list">
                            <el-form-item
                                v-for="(
                                    item, index
                                ) in weightConfigData.weightList"
                                :key="item.key"
                                :label="item.label"
                            >
                                <el-input-number
                                    v-model="
                                        weightConfigData.weightList[index].value
                                    "
                                    :min="0"
                                    :precision="2"
                                    :step="1"
                                    controls-position="right"
                                ></el-input-number>
                            </el-form-item>
                        </div>
                    </div>

                    <!-- 时间衰退配置 -->
                    <div
                        class="time-weight-section"
                        v-if="weightConfigData.timeWeightList.length > 0"
                    >
                        <h3 class="section-title">时间衰退配置</h3>
                        <el-table
                            :data="weightConfigData.timeWeightList"
                            border
                            style="width: 100%"
                            size="small"
                        >
                            <el-table-column
                                label="起始小时"
                                width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.start_hour"
                                        :min="0"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="结束小时"
                                width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.end_hour"
                                        :min="scope.row.start_hour + 1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="起始系数"
                                min-width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.start_val"
                                        :min="0"
                                        :precision="2"
                                        :step="0.1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="结束系数"
                                min-width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.end_val"
                                        :min="0"
                                        :precision="2"
                                        :step="0.1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="操作"
                                width="80"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-delete"
                                        size="mini"
                                        circle
                                        @click="removeTimePeriod(scope.$index)"
                                    ></el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-button
                            type="primary"
                            size="small"
                            icon="el-icon-plus"
                            @click="addTimePeriod"
                            style="margin-top: 10px"
                            >添加时间段</el-button
                        >
                    </div>

                    <!-- 标签配置 -->
                    <div class="label-weight-section">
                        <h3 class="section-title">标签配置</h3>
                        <el-table
                            :data="weightConfigData.labelWeightList"
                            border
                            style="width: 100%"
                            size="small"
                        >
                            <el-table-column
                                label="标签"
                                width="140"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-select
                                        v-model="scope.row.label_id"
                                        placeholder="选择标签"
                                        size="mini"
                                        filterable
                                        clearable
                                        @change="
                                            handleLabelChange(
                                                scope.$index,
                                                scope.row.label_id
                                            )
                                        "
                                    >
                                        <el-option
                                            v-for="label in labelList"
                                            :key="label.id"
                                            :label="label.name"
                                            :value="label.id"
                                        ></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="开始位置" align="center">
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.start_index"
                                        :min="1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column label="结束位置" align="center">
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.end_index"
                                        :min="scope.row.start_index || 1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column label="间隔" align="center">
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.interval"
                                        :min="1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="操作"
                                width="80"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-delete"
                                        size="mini"
                                        circle
                                        @click="removeLabelConfig(scope.$index)"
                                    ></el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-button
                            type="primary"
                            size="small"
                            icon="el-icon-plus"
                            @click="addLabelConfig"
                            style="margin-top: 10px"
                            >添加标签</el-button
                        >
                    </div>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <!-- <el-button @click="resetConfig">重置</el-button> -->
                <el-button type="primary" @click="saveConfigAndClose"
                    >保存配置</el-button
                >
            </span>
        </el-dialog>

        <!-- 评分详情弹窗 -->
        <el-dialog
            title="评分详情"
            :visible.sync="scoreDetailsDialogVisible"
            width="800px"
            height="1000px"
            :close-on-click-modal="true"
        >
            <div class="score-details-container">
                <div class="score-details-header">
                    <div class="total-score">
                        <div class="score-value">
                            {{ currentItemScore.toFixed(2) }}
                        </div>
                        <div class="score-label">总分</div>
                    </div>
                </div>
                <div class="score-details-list two-columns">
                    <div
                        v-for="(item, index) in scoreDetails"
                        :key="index"
                        class="score-detail-item"
                    >
                        <div class="score-detail-label">{{ item.label }}</div>
                        <div class="score-detail-value">{{ item.value }}</div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "GoodsSort",
    data() {
        return {
            allGoods: [],
            displayedGoods: [],
            currentPage: 1,
            pageSize: 200,
            total: 0,
            loading: false,
            hasMoreData: true,
            exposureCount: 0,
            exposedItems: new Set(),
            itemExposureCounts: {},
            observer: null,
            config: {
                ratingRatio: 10,
                favoriteRatio: 50,
                likeRatio: 40,
            },
            defaultConfig: {
                ratingRatio: 10,
                favoriteRatio: 50,
                likeRatio: 40,
            },
            weightConfigData: {
                weightList: [], // 权重列表（除时间衰退外的权重）
                timeWeightList: [], // 时间衰退配置
                labelWeightList: [], // 标签配置
            },
            scoreDetails: [],
            configDialogVisible: false,
            refreshLoading: false, // 刷新按钮加载状态
            autoRefresh: false, // 是否自动刷新
            refreshInterval: 0.084, // 刷新间隔（分钟）
            refreshTimer: null, // 定时器ID
            refreshIntervalOptions: [
                { value: 0.084, label: "5秒" },
                { value: 0.167, label: "10秒" },
                { value: 0.5, label: "30分钟" },
                { value: 1, label: "1分钟" },
                { value: 2, label: "2分钟" },
                { value: 5, label: "5分钟" },
                { value: 10, label: "10分钟" },
                { value: 20, label: "20分钟" },
                { value: 30, label: "30分钟" },
            ],
            scoreDetailsDialogVisible: false, // 评分详情弹窗是否显示
            currentItemScore: 0, // 当前选中商品的评分
            labelList: [], // 标签列表
        };
    },
    created() {
        // Initialize with page 1
        this.currentPage = 1;
    },
    mounted() {
        // 从本地存储加载自动刷新设置
        this.loadRefreshSettings();

        // Load initial data from API
        this.getProductsByPage();
        this.setupIntersectionObserver();

        // 获取标签列表
        this.getLabelList();

        // Add scroll event listener to handle pagination
        this.$nextTick(() => {
            if (this.$refs.goodsList) {
                this.$refs.goodsList.addEventListener(
                    "scroll",
                    this.handleScroll
                );
            }
        });
    },
    beforeDestroy() {
        if (this.observer) {
            this.observer.disconnect();
        }

        // Remove scroll event listener
        if (this.$refs.goodsList) {
            this.$refs.goodsList.removeEventListener(
                "scroll",
                this.handleScroll
            );
        }

        // Clear auto-refresh timer if it exists
        this.clearRefreshTimer();
    },
    computed: {},
    methods: {
        async getProductsByPage() {
            if (this.loading) return;

            this.loading = true;
            const data = {
                page: this.currentPage,
                limit: this.pageSize,
            };
            try {
                const res = await this.$request.coupons.getProductsByPage(data);
                if (res.data.error_code == 0) {
                    const newItems = res.data.data.list;
                    this.total = res.data.data.total;

                    if (this.currentPage === 1) {
                        // First page - replace all items
                        this.allGoods = newItems;
                        this.displayedGoods = [...newItems];
                    } else {
                        // Append new items to existing list
                        this.allGoods = [...this.allGoods, ...newItems];
                        this.displayedGoods = [
                            ...this.displayedGoods,
                            ...newItems,
                        ];
                    }

                    // Check if we have more data
                    this.hasMoreData = this.displayedGoods.length < this.total;

                    // Setup intersection observer for new items
                    this.$nextTick(() => {
                        this.observeItems();
                    });
                }
            } catch (error) {
                console.error("Error fetching products:", error);
            } finally {
                this.loading = false;
            }
        },
        // Method removed as we're now loading directly from API
        setupIntersectionObserver() {
            if ("IntersectionObserver" in window) {
                this.observer = new IntersectionObserver(
                    (entries) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting) {
                                const itemId = entry.target.dataset.itemId;
                                if (itemId) {
                                    const item = this.displayedGoods.find(
                                        (item) => item.id.toString() === itemId
                                    );
                                    if (item) {
                                        this.increaseExposure(item);
                                    }
                                }
                            }
                        });
                    },
                    {
                        root: this.$refs.goodsList,
                        threshold: 0.5,
                    }
                );

                this.$nextTick(() => {
                    this.observeItems();
                });
            }
        },
        observeItems() {
            const itemElements = this.$el.querySelectorAll(".goods-item");
            itemElements.forEach((el) => {
                if (this.observer) {
                    this.observer.observe(el);
                }
            });
        },
        handleScroll() {
            const element = this.$refs.goodsList;
            if (!element) return;

            const isBottom =
                element.scrollHeight - element.scrollTop <=
                element.clientHeight + 100;

            if (isBottom && !this.loading && this.hasMoreData) {
                // Increment page number and load next page
                this.currentPage++;
                this.getProductsByPage();
            }
        },
        // Method removed as we're now loading directly from API with getProductsByPage
        increaseExposure(item) {
            console.log("增加曝光:", item.id);
            if (!this.exposedItems.has(item.id)) {
                this.exposedItems.add(item.id);
                this.exposureCount++;
                this.$set(this.itemExposureCounts, item.id, 1);
            } else {
                const newCount = (this.itemExposureCounts[item.id] || 0) + 1;
                this.$set(this.itemExposureCounts, item.id, newCount);
            }
            this.$forceUpdate();
        },
        getItemExposureCount(item) {
            return this.itemExposureCounts[item.id] || 0;
        },
        adjustOtherRatios(changedRatio) {
            const otherRatios = Object.keys(this.config).filter(
                (key) => key !== changedRatio
            );

            const currentTotal = Object.values(this.config).reduce(
                (sum, val) => sum + val,
                0
            );

            if (currentTotal > 100) {
                const excess = currentTotal - 100;
                const otherTotal =
                    this.config[otherRatios[0]] + this.config[otherRatios[1]];

                if (otherTotal > 0) {
                    const ratio1 = this.config[otherRatios[0]] / otherTotal;
                    const ratio2 = this.config[otherRatios[1]] / otherTotal;

                    this.config[otherRatios[0]] = Math.max(
                        0,
                        Math.floor(
                            this.config[otherRatios[0]] - excess * ratio1
                        )
                    );
                    this.config[otherRatios[1]] = Math.max(
                        0,
                        Math.floor(
                            this.config[otherRatios[1]] - excess * ratio2
                        )
                    );

                    const newTotal = Object.values(this.config).reduce(
                        (sum, val) => sum + val,
                        0
                    );
                    if (newTotal < 100) {
                        this.config[otherRatios[0]] += 100 - newTotal;
                    } else if (newTotal > 100) {
                        this.config[otherRatios[0]] = Math.max(
                            0,
                            this.config[otherRatios[0]] - (newTotal - 100)
                        );
                    }
                } else {
                    this.config[changedRatio] = 100;
                }
            }
        },
        async openConfigDialog() {
            // 获取权重配置
            await this.weightConfig();
            // 显示配置弹窗
            this.configDialogVisible = true;
        },

        async saveConfig() {
            // 验证时间衰退配置
            const timePeriods = this.weightConfigData.timeWeightList;
            if (timePeriods.length === 0) {
                this.$message.error(
                    "时间衰退配置不能为空，请至少添加一个时间段。"
                );
                return false;
            }

            // 检查时间段是否覆盖24小时且没有重叠
            // 按照起始小时排序，确保检查顺序
            timePeriods.sort((a, b) => a.start_hour - b.start_hour);

            for (let i = 0; i < timePeriods.length; i++) {
                const period = timePeriods[i];
                if (period.start_hour === null || period.end_hour === null) {
                    this.$message.error("时间段的起始小时和结束小时不能为空。");
                    return false;
                }

                if (period.start_hour > period.end_hour) {
                    this.$message.error("时间段的起始小时不能大于结束小时。");
                    return false;
                }

                // 检查与上一个时间段的衔接
                if (i > 0) {
                    const prevPeriod = timePeriods[i - 1];
                    if (period.start_hour < prevPeriod.end_hour) {
                        this.$message.error(
                            `时间段 ${period.start_hour}-${period.end_hour} 的起始小时不能小于上一个时间段 ${prevPeriod.start_hour}-${prevPeriod.end_hour} 的结束小时。`
                        );
                        return false;
                    }
                }
            }

            // 检查是否覆盖24小时
            const hoursCovered = new Array(24).fill(false);
            for (const period of timePeriods) {
                for (let i = period.start_hour; i <= period.end_hour; i++) {
                    hoursCovered[i] = true;
                }
            }

            for (let i = 0; i < 24; i++) {
                if (!hoursCovered[i]) {
                    this.$message.error(
                        `时间段未覆盖到所有24小时，小时 ${i} 未被覆盖。`
                    );
                    return false;
                }
            }

            try {
                // 构建保存参数
                const params = {
                    cf: {}, // 使用cf作为顶层包装对象
                };

                // 处理普通权重
                this.weightConfigData.weightList.forEach((item) => {
                    // 保持完整的对象结构，包含label和value
                    params.cf[item.key] = {
                        label: item.label,
                        value: item.value,
                    };
                });

                // 处理时间衰退配置
                params.cf.time_weight =
                    this.weightConfigData.timeWeightList.map((item) => ({
                        label: item.label,
                        start_hour: item.start_hour,
                        end_hour: item.end_hour,
                        start_val: item.start_val,
                        end_val: item.end_val,
                    }));

                // 处理标签配置
                params.cf.label_weight =
                    this.weightConfigData.labelWeightList.map((item) => ({
                        label_id: item.label_id,
                        label: item.label,
                        start_index: item.start_index,
                        end_index: item.end_index,
                        interval: item.interval,
                    }));

                // 调用保存接口
                const res = await this.$request.coupons.saveWeightConfig(
                    params
                );

                if (res.data.error_code === 0) {
                    this.$message.success("配置保存成功");
                    return true;
                } else {
                    this.$message.error(res.data.error_msg || "保存失败");
                    return false;
                }
            } catch (error) {
                console.error("保存权重配置失败:", error);
                this.$message.error("保存失败，请重试");
                return false;
            }
        },
        async saveConfigAndClose() {
            const success = await this.saveConfig();
            if (success) {
                this.configDialogVisible = false;
            }
        },
        async resetConfig() {
            try {
                // 重新获取配置
                await this.weightConfig();
                this.$message.info("配置已重置");
            } catch (error) {
                console.error("重置配置失败:", error);
                this.$message.error("重置失败，请重试");
            }
        },
        async weightConfig() {
            try {
                // 获取权重配置
                const res = await this.$request.coupons.weightConfig();

                if (res.data.error_code === 0) {
                    // 获取cf对象中的数据
                    const data = res.data.data.cf || res.data.data;

                    // 处理除时间衰退外的权重配置
                    const weightList = [];
                    for (const key in data) {
                        if (
                            key !== "time_weight" &&
                            key !== "label_weight" &&
                            data[key]
                        ) {
                            weightList.push({
                                key: key,
                                label: data[key].label,
                                value: data[key].value,
                            });
                        }
                    }

                    // 设置权重列表
                    this.weightConfigData.weightList = weightList;

                    // 设置时间衰退配置
                    if (data.time_weight && Array.isArray(data.time_weight)) {
                        this.weightConfigData.timeWeightList =
                            data.time_weight.map((item) => ({
                                ...item,
                                label:
                                    item.label ||
                                    `${item.start_hour}-${item.end_hour}点`, // Add a default label if missing
                            }));
                    }

                    // 设置标签配置
                    if (data.label_weight && Array.isArray(data.label_weight)) {
                        this.weightConfigData.labelWeightList =
                            data.label_weight.map((item) => ({
                                ...item,
                            }));
                    } else {
                        this.weightConfigData.labelWeightList = [];
                    }

                    return true;
                } else {
                    this.$message.error(
                        res.data.error_msg || "获取权重配置失败"
                    );
                    return false;
                }
            } catch (error) {
                console.error("获取权重配置失败:", error);
                this.$message.error("获取权重配置失败，请重试");
                return false;
            }
        },

        // 获取标签列表
        async getLabelList() {
            try {
                const params = {
                    type: 2,
                    page: 1,
                    limit: 100,
                };
                const res = await this.$request.bullein.getLabelList(params);
                if (res.data.error_code === 0) {
                    this.labelList = (res.data.data.list || []).map((item) => ({
                        id: item.id,
                        name: item.name,
                    }));
                }
            } catch (error) {
                console.error("获取标签列表失败:", error);
                this.$message.error("获取标签列表失败");
            }
        },

        // 保存刷新设置到本地存储
        saveRefreshSettings() {
            try {
                const settings = {
                    autoRefresh: this.autoRefresh,
                    refreshInterval: this.refreshInterval,
                };
                localStorage.setItem(
                    "goodsSortRefreshSettings",
                    JSON.stringify(settings)
                );
                console.log("刷新设置已保存到本地存储");
            } catch (error) {
                console.error("保存刷新设置失败:", error);
            }
        },

        // 从本地存储加载刷新设置
        loadRefreshSettings() {
            try {
                const settingsStr = localStorage.getItem(
                    "goodsSortRefreshSettings"
                );
                if (settingsStr) {
                    const settings = JSON.parse(settingsStr);
                    this.autoRefresh = settings.autoRefresh || false;
                    this.refreshInterval = settings.refreshInterval || 5;

                    // 如果自动刷新开启，启动定时器
                    if (this.autoRefresh) {
                        this.$nextTick(() => {
                            this.startRefreshTimer();
                        });
                    }
                    console.log("已从本地存储加载刷新设置");
                }
            } catch (error) {
                console.error("加载刷新设置失败:", error);
                // 出错时使用默认设置
                this.autoRefresh = false;
                this.refreshInterval = 5;
            }
        },

        // 处理自动刷新开关变化
        handleAutoRefreshChange(value) {
            if (value) {
                this.startRefreshTimer();
            } else {
                this.clearRefreshTimer();
            }

            // 保存设置到本地存储
            this.saveRefreshSettings();
        },

        // 处理刷新间隔变化
        handleIntervalChange() {
            if (this.autoRefresh) {
                // 重新启动定时器以应用新的间隔
                this.clearRefreshTimer();
                this.startRefreshTimer();
            }

            // 保存设置到本地存储
            this.saveRefreshSettings();
        },

        // 启动刷新定时器
        startRefreshTimer() {
            this.clearRefreshTimer(); // 先清除现有定时器

            // 将分钟转换为毫秒
            const intervalMs = this.refreshInterval * 60 * 1000;

            this.refreshTimer = setInterval(() => {
                this.refreshData(false); // 不显示成功提示
            }, intervalMs);

            console.log(`已启动自动刷新，间隔: ${this.refreshInterval} 分钟`);
        },

        // 清除刷新定时器
        clearRefreshTimer() {
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
                this.refreshTimer = null;
                console.log("已停止自动刷新");
            }
        },

        // 刷新数据
        async refreshData(showMessage = true) {
            if (this.refreshLoading) return;

            this.refreshLoading = true;
            try {
                // 重置商品列表到第一页
                this.currentPage = 1;
                await this.getProductsByPage();

                if (showMessage) {
                    this.$message.success("刷新成功");
                }
            } catch (error) {
                console.error("刷新数据失败:", error);
                if (showMessage) {
                    this.$message.error("刷新数据失败");
                }
            } finally {
                this.refreshLoading = false;
            }
        },

        // 显示评分详情弹窗
        showScoreDetails(item) {
            if (!item || typeof item.score === "undefined") {
                this.$message.error("无法获取评分详情");
                return;
            }

            // 设置当前商品评分
            this.currentItemScore = item.score;

            // 显示弹窗
            this.scoreDetails = item.scoreDetails;
            this.scoreDetailsDialogVisible = true;
        },

        // 复制文本到剪贴板
        copyToClipboard(text) {
            // 使用现代的Clipboard API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard
                    .writeText(text)
                    .then(() => {
                        // 显示成功提示
                        this.$message.success(`已复制期数: ${text}`);
                    })
                    .catch((err) => {
                        console.error("复制失败:", err);
                        this.$message.error("复制失败，请手动复制");
                        this.fallbackCopyToClipboard(text);
                    });
            } else {
                // 回退到旧方法
                this.fallbackCopyToClipboard(text);
            }
        },

        // 回退的复制方法
        fallbackCopyToClipboard(text) {
            // 创建一个临时的textarea元素
            const textarea = document.createElement("textarea");
            textarea.value = text;
            textarea.setAttribute("readonly", "");
            textarea.style.position = "absolute";
            textarea.style.left = "-9999px";
            document.body.appendChild(textarea);

            // 选择文本并复制
            textarea.select();
            try {
                document.execCommand("copy");
                this.$message.success(`已复制期数: ${text}`);
            } catch (err) {
                console.error("复制失败:", err);
                this.$message.error("复制失败，请手动复制");
            }

            // 移除临时元素
            document.body.removeChild(textarea);
        },

        // 添加时间段
        addTimePeriod() {
            this.weightConfigData.timeWeightList.push({
                label: "自定义",
                start_hour: 0,
                end_hour: 0,
                start_val: 1,
                end_val: 1,
            });
        },

        // 移除时间段
        removeTimePeriod(index) {
            this.weightConfigData.timeWeightList.splice(index, 1);
        },

        // 添加标签配置
        addLabelConfig() {
            this.weightConfigData.labelWeightList.push({
                label_id: null,
                label: "",
                start_index: 10,
                end_index: 20,
                interval: 2,
            });
        },

        // 移除标签配置
        removeLabelConfig(index) {
            this.weightConfigData.labelWeightList.splice(index, 1);
        },

        // 处理标签选择变化
        handleLabelChange(index, labelId) {
            const selectedLabel = this.labelList.find(
                (label) => label.id === labelId
            );
            if (selectedLabel) {
                this.weightConfigData.labelWeightList[index].label =
                    selectedLabel.name;
            }
        },

        // 获取频道名称
        getChannelName(periodsType) {
            const channelMap = {
                0: "闪购",
                1: "秒发",
                2: "跨境",
                3: "尾货",
            };
            return channelMap[periodsType] || "未知";
        },

        // 获取频道标签类型（用于不同颜色显示）
        getChannelTagType(periodsType) {
            const typeMap = {
                0: "success", // 闪购 - 绿色
                1: "danger", // 秒发 - 红色
                2: "primary", // 跨境 - 蓝色
                3: "warning", // 尾货 - 橙色
            };
            return typeMap[periodsType] || "info";
        },
    },
};
</script>

<style lang="scss" scoped>
.goods-sort-container {
    padding: 20px;

    .top-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .action-buttons {
            display: flex;
            gap: 10px;
            align-items: center;

            .auto-refresh-container {
                display: flex;
                align-items: center;
                margin-left: 15px;
                background-color: #f8f8f8;
                padding: 5px 10px;
                border-radius: 4px;

                .el-checkbox {
                    margin-right: 10px;
                }
                /deep/ label {
                    margin: 0 !important;
                }
                .el-select {
                    margin: 10px;
                    width: 100px;
                }
            }
        }
    }

    .content-wrapper {
        display: flex;
        flex-direction: column;
        gap: 20px;

        @media screen and (min-width: 768px) {
            flex-direction: row;
            justify-content: space-between;
        }
    }

    .goods-list-container {
        width: 100%;
        max-width: 375px; /* 标准H5宽度 */
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 80px);
        border-radius: 20px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
        background-color: #f8f8f8;

        /* 手机顶部状态栏 */
        &::before {
            content: "";
            display: block;
            height: 25px;
            background-color: #fff;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            border-bottom: 1px solid #eee;
        }

        /* 手机底部导航条 */
        &::after {
            content: "";
            display: block;
            height: 5px;
            width: 40%;
            background-color: #ddd;
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 5px;
            z-index: 10;
        }

        @media screen and (min-width: 768px) {
            flex: 0 0 375px; /* 固定宽度 */
            margin: 0;
        }

        .goods-list {
            flex: 1;
            overflow-y: auto;
            padding: 30px 10px 40px;
            margin-right: -5px;
            scroll-behavior: smooth;

            /* 滚动条样式 */
            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #ddd;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb:hover {
                background: #ccc;
            }
        }

        /* 移除曝光值显示样式 */
        .exposure-counter {
            display: none;
        }
    }

    .config-form {
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }

        .weight-section {
            margin-bottom: 20px;

            .weight-list {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 15px;
            }
        }

        .time-weight-section {
            margin-top: 30px;

            .el-table .el-table__cell {
                padding: 8px 0; // Reduce padding in table cells
            }

            /deep/ .el-input-number {
                width: 100%; // Make the input number adaptive to cell width

                .el-input__inner {
                    padding-left: 5px; // Reduce left padding of the input
                    padding-right: 25px; // Make space for controls
                }

                .el-input-number__decrease,
                .el-input-number__increase {
                    width: 20px; // Make control buttons smaller
                }
            }
        }

        .config-summary {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .el-progress {
                margin-bottom: 15px;
            }
        }
    }

    .goods-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .goods-item {
        position: relative;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        width: 100%;
        max-width: 355px; /* 适合容器的宽度 */
        margin: 0 auto 0px; /* 减少商品之间的间距 */
        transition: transform 0.3s, box-shadow 0.3s;

        &:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
        }

        .goods-image {
            width: 100%;
            height: 160px; /* 减小图片高度，使页面能够显示更多商品 */
            overflow: hidden;
            position: relative;

            &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 30px;
                background: linear-gradient(
                    to top,
                    rgba(0, 0, 0, 0.15),
                    transparent
                );
                pointer-events: none;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.5s;
                border-radius: 4px;

                &:hover {
                    transform: scale(1.05);
                }
            }

            /* 商品曝光标识 - 左上角 */
            .item-exposure-badge {
                position: absolute;
                top: 8px;
                left: 8px;
                min-width: 22px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                background-color: rgba(255, 72, 0, 0.9);
                color: #fff;
                font-size: 12px;
                font-weight: bold;
                padding: 0 6px;
                border-radius: 11px;
                z-index: 100;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            /* 商品期数标识 - 右上角 */
            .item-period-badge {
                position: absolute;
                top: 8px;
                right: 8px;
                min-width: 22px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                background-color: rgba(64, 158, 255, 0.9);
                color: #fff;
                font-size: 12px;
                font-weight: bold;
                padding: 0 6px;
                border-radius: 11px;
                z-index: 100;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                cursor: pointer;
            }
        }

        .goods-info {
            padding: 12px; /* 减少内边距 */
            display: flex;
            flex-direction: column;

            .goods-header {
                margin-bottom: 6px; /* 减少间距 */

                .goods-tags {
                    margin-bottom: 6px;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;

                    .tag-item {
                        display: inline-block;
                        padding: 2px 8px;
                        background-color: #ff9900;
                        color: #fff;
                        font-size: 12px;
                        border-radius: 20px;
                        letter-spacing: 0.5px;
                    }
                }

                .goods-title {
                    margin: 0;
                    font-size: 15px; /* 稍微减小字体 */
                    font-weight: 600;
                    color: #333;
                    line-height: 1.3;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .goods-desc {
                margin-bottom: 6px;
                font-size: 13px; /* 减小字体 */
                color: #666;
                line-height: 1.4;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2; /* 减少显示行数 */
                line-clamp: 2; /* 标准属性 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                p {
                    margin-bottom: 0;
                }
            }

            .goods-features {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                margin-bottom: 8px;

                .feature-tag {
                    padding: 2px 8px;
                    background-color: #f5f5f5;
                    color: #666;
                    font-size: 11px; /* 减小字体 */
                    border-radius: 20px;
                }
            }

            .goods-labels {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                margin-bottom: 8px;

                .goods-label-tag {
                    font-size: 10px;

                    border-radius: 12px;
                    background-color: #e8f4fd;
                    border-color: #b3d8ff;
                    color: #409eff;
                }
            }

            // 频道和投放类型信息样式
            .goods-channel-info {
                margin-bottom: 8px;
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                align-items: center;

                .channel-tag {
                    font-weight: 600;
                    border-radius: 12px;

                    font-size: 10px;
                }

                .sort-type-tag {
                    font-weight: 500;
                    border-radius: 12px;

                    font-size: 10px;

                    &.no-sort {
                        opacity: 0.7;
                        font-style: italic;
                    }
                }
            }

            .goods-footer {
                margin-top: 6px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 6px;
                border-top: 1px solid #f5f5f5;

                .goods-price {
                    color: #ff6600;

                    .price-symbol {
                        font-size: 13px;
                    }

                    .price-value {
                        font-size: 20px; /* 减小字体 */
                        font-weight: 700;
                    }
                }

                .goods-sales {
                    font-size: 11px;
                    color: #999;
                    background-color: #f9f9f9;
                    padding: 2px 6px;
                    border-radius: 20px;
                }
            }
        }
    }

    .loading-indicator {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 14px;
    }

    .no-more-data {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 14px;
    }

    /* 评分详情弹窗样式 */
    .score-details-container {
        padding: 10px;

        .score-details-header {
            text-align: center;
            margin-bottom: 20px;

            .total-score {
                display: inline-block;

                .score-value {
                    font-size: 36px;
                    font-weight: bold;
                    color: #409eff;
                }

                .score-label {
                    font-size: 14px;
                    color: #606266;
                    margin-top: 5px;
                }
            }
        }

        .score-details-list {
            max-height: 400px;
            overflow-y: auto;

            &.two-columns {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px 20px;
            }

            .score-detail-item {
                display: flex;
                justify-content: space-between;
                padding: 6px 15px;
                border-bottom: 1px solid #ebeef5;

                &:last-child {
                    border-bottom: none;
                }

                .score-detail-label {
                    flex: 3;
                    font-size: 14px;
                    color: #606266;
                    margin-right: 10px;
                }

                .score-detail-value {
                    flex: 1;
                    text-align: right;
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                }
            }
        }
    }

    /* 添加鼠标指针样式，表示可点击 */
    .item-exposure-badge,
    .item-period-badge,
    .promoted-score {
        cursor: pointer;

        &:hover {
            opacity: 0.8;
        }
    }
}
</style>
