import axios from "axios";

function getCustomActivityList(data) {
    // 获取自选专题活动列表
    return axios({
        url: "/api/activity/v3/custom_activity/list",
        method: "get",
        params: data,
    });
}

function addCustomActivity(data) {
    // 新增自选专题活动
    return axios({
        url: "/api/activity/v3/custom_activity/create",
        method: "post",
        data,
    });
}

function updateCustomActivity(data) {
    // 编辑自选专题活动
    return axios({
        url: "/api/activity/v3/custom_activity/update",
        method: "post",
        data,
    });
}

function getCustomActivityGoodsList(data) {
    // 获取自选专题商品列表
    return axios({
        url: "/api/activity/v3/custom_activity/goods/list",
        method: "get",
        params: data,
    });
}

function createCustomActivityGoods(data) {
    // 创建自选专题商品
    return axios({
        url: "/api/activity/v3/custom_activity/goods/create",
        method: "post",
        data,
    });
}

function updateCustomActivityGoods(data) {
    // 编辑自选专题商品
    return axios({
        url: "/api/activity/v3/custom_activity/goods/update",
        method: "post",
        data,
    });
}

function getCustomActivityPackage(data) {
    // 根据期数获取支持自选套餐
    return axios({
        url: "/api/commodities/v3/package/getCustomActivityPackage",
        method: "get",
        params: data,
    });
}
function miniProgramCode(data) {
    // 生成二维码
    return axios({
        url: "/api/activity/v3/custom_activity/miniProgramCode",
        method: "get",
        params: data,
    });
}

export default {
    getCustomActivityList,
    miniProgramCode,
    addCustomActivity,
    updateCustomActivity,
    getCustomActivityGoodsList,
    createCustomActivityGoods,
    getCustomActivityPackage,
    updateCustomActivityGoods,
};
